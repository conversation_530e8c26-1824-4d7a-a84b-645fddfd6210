import { useState, useEffect } from 'react';
import { User, MapPin, Calendar, ChevronDown, Search, Loader2 } from 'lucide-react';
import { GlassCard } from '@/components/GlassCard';
import { GlassButton } from '@/components/ui/glass-button';
import { GlassInput } from '@/components/ui/glass-input';
import { cn } from '@/lib/utils';
import { UserProfileFormData, UserProfileFormErrors, CitySearchResult, WeatherData } from '@/types/user';
import { searchCities, getWeatherData, debounce } from '@/services/api';

interface UserProfileFormProps {
  onNext: (data: UserProfileFormData) => void;
  onPrevious: () => void;
  onSkip: () => void;
  canSkip?: boolean;
  currentStep: number;
  totalSteps: number;
}

export const UserProfileForm = ({ 
  onNext, 
  onPrevious, 
  onSkip, 
  canSkip = false,
  currentStep,
  totalSteps 
}: UserProfileFormProps) => {
  const [formData, setFormData] = useState<UserProfileFormData>({
    firstName: '',
    lastName: '',
    gender: '',
    dateOfBirth: '',
    cityName: '',
  });

  const [errors, setErrors] = useState<UserProfileFormErrors>({});
  const [isLoading, setIsLoading] = useState(false);
  const [citySearchResults, setCitySearchResults] = useState<CitySearchResult[]>([]);
  const [showCityDropdown, setShowCityDropdown] = useState(false);
  const [isSearchingCities, setIsSearchingCities] = useState(false);
  const [weatherData, setWeatherData] = useState<WeatherData | null>(null);
  const [isLoadingWeather, setIsLoadingWeather] = useState(false);

  const genderOptions = [
    { value: 'Male', label: 'Male' },
    { value: 'Female', label: 'Female' },
    { value: 'Other', label: 'Other' },
    { value: 'Prefer not to say', label: 'Prefer not to say' },
  ];

  // Debounced city search
  const debouncedCitySearch = debounce(async (query: string) => {
    if (query.length < 2) {
      setCitySearchResults([]);
      setShowCityDropdown(false);
      return;
    }

    setIsSearchingCities(true);
    try {
      const results = await searchCities(query);
      setCitySearchResults(results);
      setShowCityDropdown(results.length > 0);
    } catch (error) {
      console.error('City search error:', error);
      setCitySearchResults([]);
      setShowCityDropdown(false);
    } finally {
      setIsSearchingCities(false);
    }
  }, 300);

  // Handle city input change
  const handleCityInputChange = (value: string) => {
    setFormData(prev => ({ ...prev, cityName: value, selectedCity: undefined }));
    debouncedCitySearch(value);
  };

  // Handle city selection
  const handleCitySelect = async (city: CitySearchResult) => {
    setFormData(prev => ({
      ...prev,
      cityName: city.formattedName,
      selectedCity: city
    }));
    setShowCityDropdown(false);
    setCitySearchResults([]);

    // Fetch weather data for selected city (optional, non-blocking)
    if (import.meta.env.VITE_ENABLE_WEATHER_INTEGRATION === 'true') {
      setIsLoadingWeather(true);
      try {
        const weather = await getWeatherData(parseFloat(city.lat), parseFloat(city.lon));
        setWeatherData(weather);
        console.log('Weather data loaded successfully for', city.formattedName);
      } catch (error) {
        console.warn('Weather fetch failed (non-blocking):', error);
        // Weather is optional, don't show error to user or block the flow
        setWeatherData(null);
      } finally {
        setIsLoadingWeather(false);
      }
    }
  };

  // Form validation
  const validateForm = (): boolean => {
    const newErrors: UserProfileFormErrors = {};

    if (!formData.firstName.trim()) {
      newErrors.firstName = 'First name is required';
    }

    if (!formData.lastName.trim()) {
      newErrors.lastName = 'Last name is required';
    }

    if (!formData.gender) {
      newErrors.gender = 'Please select your gender';
    }

    if (!formData.dateOfBirth) {
      newErrors.dateOfBirth = 'Date of birth is required';
    } else {
      const birthDate = new Date(formData.dateOfBirth);
      const today = new Date();
      const age = today.getFullYear() - birthDate.getFullYear();
      
      if (age < 13) {
        newErrors.dateOfBirth = 'You must be at least 13 years old';
      } else if (age > 120) {
        newErrors.dateOfBirth = 'Please enter a valid date of birth';
      }
    }

    if (!formData.cityName.trim()) {
      newErrors.cityName = 'City is required';
    } else if (!formData.selectedCity) {
      newErrors.cityName = 'Please select a city from the dropdown';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async () => {
    if (!validateForm()) return;

    setIsLoading(true);
    try {
      // Add a small delay for better UX
      await new Promise(resolve => setTimeout(resolve, 500));
      onNext(formData);
    } catch (error) {
      setErrors({ general: 'An error occurred. Please try again.' });
    } finally {
      setIsLoading(false);
    }
  };

  // Calculate max date (today)
  const maxDate = new Date().toISOString().split('T')[0];
  
  // Calculate min date (120 years ago)
  const minDate = new Date();
  minDate.setFullYear(minDate.getFullYear() - 120);
  const minDateString = minDate.toISOString().split('T')[0];

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="text-center mb-8">
        <div className="w-16 h-16 mx-auto mb-4 glass-medium rounded-full flex items-center justify-center">
          <User size={24} className="text-zara-charcoal" />
        </div>
        <h1 className="zara-hero mb-2">Tell us about yourself</h1>
        <p className="zara-body text-zara-dark-gray">
          Help us personalize your experience
        </p>
      </div>

      {/* Progress indicator */}
      <div className="flex justify-center mb-8">
        <div className="flex items-center space-x-2">
          {Array.from({ length: totalSteps }, (_, i) => (
            <div
              key={i}
              className={cn(
                "w-2 h-2 rounded-full transition-all duration-300",
                i < currentStep ? "glass-strong" : "glass-subtle"
              )}
            />
          ))}
        </div>
      </div>

      {/* Form */}
      <div className="flex-1 space-y-6">
        {/* Name fields */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block zara-body text-zara-dark-gray mb-2">
              First Name *
            </label>
            <GlassInput
              value={formData.firstName}
              onChange={(e) => setFormData(prev => ({ ...prev, firstName: e.target.value }))}
              placeholder="Enter your first name"
              className={errors.firstName ? 'border-red-300' : ''}
            />
            {errors.firstName && (
              <p className="text-red-500 zara-caption mt-1">{errors.firstName}</p>
            )}
          </div>

          <div>
            <label className="block zara-body text-zara-dark-gray mb-2">
              Last Name *
            </label>
            <GlassInput
              value={formData.lastName}
              onChange={(e) => setFormData(prev => ({ ...prev, lastName: e.target.value }))}
              placeholder="Enter your last name"
              className={errors.lastName ? 'border-red-300' : ''}
            />
            {errors.lastName && (
              <p className="text-red-500 zara-caption mt-1">{errors.lastName}</p>
            )}
          </div>
        </div>

        {/* Gender selection */}
        <div>
          <label className="block zara-body text-zara-dark-gray mb-2">
            Gender *
          </label>
          <div className="relative">
            <select
              value={formData.gender}
              onChange={(e) => setFormData(prev => ({ ...prev, gender: e.target.value }))}
              className={cn(
                "w-full h-12 px-4 glass-panel rounded-xl zara-body appearance-none cursor-pointer",
                "focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary/30",
                errors.gender ? 'border-red-300' : ''
              )}
            >
              <option value="">Select your gender</option>
              {genderOptions.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
            <ChevronDown size={20} className="absolute right-4 top-1/2 transform -translate-y-1/2 text-zara-dark-gray pointer-events-none" />
          </div>
          {errors.gender && (
            <p className="text-red-500 zara-caption mt-1">{errors.gender}</p>
          )}
        </div>

        {/* Date of birth */}
        <div>
          <label className="block zara-body text-zara-dark-gray mb-2">
            Date of Birth *
          </label>
          <div className="relative">
            <GlassInput
              type="date"
              value={formData.dateOfBirth}
              onChange={(e) => setFormData(prev => ({ ...prev, dateOfBirth: e.target.value }))}
              min={minDateString}
              max={maxDate}
              className={cn(
                errors.dateOfBirth ? 'border-red-300' : '',
                "pr-12"
              )}
            />
            <Calendar size={20} className="absolute right-4 top-1/2 transform -translate-y-1/2 text-zara-dark-gray pointer-events-none" />
          </div>
          {errors.dateOfBirth && (
            <p className="text-red-500 zara-caption mt-1">{errors.dateOfBirth}</p>
          )}
        </div>

        {/* City search */}
        <div>
          <label className="block zara-body text-zara-dark-gray mb-2">
            City *
          </label>
          <div className="relative">
            <div className="relative">
              <GlassInput
                value={formData.cityName}
                onChange={(e) => handleCityInputChange(e.target.value)}
                placeholder="Search for your city..."
                className={cn(
                  errors.cityName ? 'border-red-300' : '',
                  "pl-12"
                )}
                onFocus={() => formData.cityName.length >= 2 && setShowCityDropdown(citySearchResults.length > 0)}
              />
              <div className="absolute left-4 top-1/2 transform -translate-y-1/2">
                {isSearchingCities ? (
                  <Loader2 size={20} className="text-zara-dark-gray animate-spin" />
                ) : (
                  <Search size={20} className="text-zara-dark-gray" />
                )}
              </div>
            </div>

            {/* City dropdown */}
            {showCityDropdown && citySearchResults.length > 0 && (
              <div className="absolute top-full left-0 right-0 z-50 mt-1">
                <GlassCard className="max-h-48 overflow-y-auto">
                  {citySearchResults.map((city) => (
                    <button
                      key={city.place_id}
                      onClick={() => handleCitySelect(city)}
                      className="w-full p-3 text-left hover:glass-light transition-all duration-200 flex items-center space-x-3"
                    >
                      <MapPin size={16} className="text-zara-dark-gray flex-shrink-0" />
                      <span className="zara-body">{city.formattedName}</span>
                    </button>
                  ))}
                </GlassCard>
              </div>
            )}
          </div>
          {errors.cityName && (
            <p className="text-red-500 zara-caption mt-1">{errors.cityName}</p>
          )}
        </div>

        {/* Weather confirmation */}
        {weatherData && (
          <GlassCard className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 glass-subtle rounded-full flex items-center justify-center">
                  <MapPin size={16} className="text-zara-charcoal" />
                </div>
                <div>
                  <p className="zara-subtitle">Location confirmed</p>
                  <p className="zara-body text-zara-dark-gray">
                    {weatherData.name}, {weatherData.sys.country}
                  </p>
                </div>
              </div>
              <div className="text-right">
                <p className="zara-subtitle">{Math.round(weatherData.main.temp)}°C</p>
                <p className="zara-caption text-zara-dark-gray">
                  {weatherData.weather[0].description}
                </p>
              </div>
            </div>
          </GlassCard>
        )}

        {errors.general && (
          <div className="p-4 glass-subtle rounded-xl border border-red-300">
            <p className="text-red-500 zara-body">{errors.general}</p>
          </div>
        )}
      </div>

      {/* Navigation buttons */}
      <div className="flex justify-between items-center pt-8">
        <GlassButton
          variant="secondary"
          onClick={onPrevious}
          className="px-8"
        >
          Back
        </GlassButton>

        <div className="flex space-x-4">
          {canSkip && (
            <GlassButton
              variant="ghost"
              onClick={onSkip}
            >
              Skip
            </GlassButton>
          )}
          
          <GlassButton
            variant="primary"
            onClick={handleSubmit}
            disabled={isLoading}
            className="px-8"
          >
            {isLoading ? (
              <div className="flex items-center space-x-2">
                <Loader2 size={16} className="animate-spin" />
                <span>Saving...</span>
              </div>
            ) : (
              'Continue'
            )}
          </GlassButton>
        </div>
      </div>
    </div>
  );
};

import express from 'express';
import { AuthService, AuthCredentials } from '../services/authService.js';
import { getDatabase } from '../database/connection.js';
import { logger } from '../utils/logger.js';

const router = express.Router();
const authService = new AuthService(getDatabase());

/**
 * POST /api/auth/register
 * Register a new user
 */
router.post('/register', async (req, res) => {
  const requestId = req.headers['x-request-id'] as string;
  
  try {
    const { email, password }: AuthCredentials = req.body;

    // Validate request body
    if (!email || !password) {
      return res.status(400).json({
        success: false,
        error: 'Email and password are required'
      });
    }

    // Register user
    const result = await authService.register({ email, password }, requestId);

    if (result.success) {
      logger.info('AUTH', 'User registered successfully', {
        userId: result.user?.id,
        email: result.user?.email
      }, requestId);

      res.status(201).json({
        success: true,
        user: {
          id: result.user?.id,
          email: result.user?.email,
          createdAt: result.user?.createdAt
        }
      });
    } else {
      res.status(400).json({
        success: false,
        error: result.error
      });
    }

  } catch (error) {
    logger.error('AUTH', 'Registration endpoint error', {
      error: error instanceof Error ? error.message : 'Unknown error'
    }, error instanceof Error ? error : undefined, requestId);

    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * POST /api/auth/login
 * Sign in an existing user
 */
router.post('/login', async (req, res) => {
  const requestId = req.headers['x-request-id'] as string;
  
  try {
    const { email, password }: AuthCredentials = req.body;

    // Validate request body
    if (!email || password === undefined) {
      return res.status(400).json({
        success: false,
        error: 'Email and password are required'
      });
    }

    // Sign in user
    const result = await authService.signIn({ email, password }, requestId);

    if (result.success) {
      logger.info('AUTH', 'User signed in successfully', {
        userId: result.user?.id,
        email: result.user?.email
      }, requestId);

      res.json({
        success: true,
        user: {
          id: result.user?.id,
          email: result.user?.email,
          lastLoginAt: result.user?.lastLoginAt
        }
      });
    } else {
      res.status(401).json({
        success: false,
        error: result.error
      });
    }

  } catch (error) {
    logger.error('AUTH', 'Login endpoint error', {
      error: error instanceof Error ? error.message : 'Unknown error'
    }, error instanceof Error ? error : undefined, requestId);

    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * GET /api/auth/user/:id
 * Get user by ID
 */
router.get('/user/:id', async (req, res) => {
  const requestId = req.headers['x-request-id'] as string;
  
  try {
    const { id } = req.params;

    if (!id) {
      return res.status(400).json({
        success: false,
        error: 'User ID is required'
      });
    }

    const user = await authService.getUserById(id, requestId);

    if (user) {
      res.json({
        success: true,
        user: {
          id: user.id,
          email: user.email,
          createdAt: user.createdAt,
          lastLoginAt: user.lastLoginAt
        }
      });
    } else {
      res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }

  } catch (error) {
    logger.error('AUTH', 'Get user endpoint error', {
      userId: req.params.id,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, error instanceof Error ? error : undefined, requestId);

    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * POST /api/auth/validate-email
 * Validate if email is available
 */
router.post('/validate-email', async (req, res) => {
  const requestId = req.headers['x-request-id'] as string;
  
  try {
    const { email } = req.body;

    if (!email) {
      return res.status(400).json({
        success: false,
        error: 'Email is required'
      });
    }

    const existingUser = await authService.getUserByEmail(email, requestId);

    res.json({
      success: true,
      available: !existingUser,
      message: existingUser ? 'Email is already registered' : 'Email is available'
    });

  } catch (error) {
    logger.error('AUTH', 'Email validation endpoint error', {
      error: error instanceof Error ? error.message : 'Unknown error'
    }, error instanceof Error ? error : undefined, requestId);

    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

export { router as authRouter };

import React, { useState, useEffect } from "react";
import { ChevronLeft, ChevronRight, Sun, Calendar, Shirt, Home, Grid3X3, Play, Pause } from "lucide-react";

const MockupShowcase = () => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);

  const mockups = [
    {
      title: "Home Dashboard",
      description: "Weather-aware outfit recommendations at a glance",
      icon: Home,
      features: ["Weather Integration", "Daily Recommendations", "Quick Access"],
      mockupType: "dashboard",
      gradient: "from-blue-500/20 to-purple-500/20"
    },
    {
      title: "Smart Closet",
      description: "Organize and browse your wardrobe effortlessly",
      icon: Shirt,
      features: ["Visual Organization", "Smart Categories", "Quick Search"],
      mockupType: "closet",
      gradient: "from-emerald-500/20 to-teal-500/20"
    },
    {
      title: "Schedule Planner",
      description: "Plan your outfits for upcoming events and occasions",
      icon: Calendar,
      features: ["Event Planning", "Outfit Scheduling", "Smart Suggestions"],
      mockupType: "schedule",
      gradient: "from-orange-500/20 to-red-500/20"
    },
    {
      title: "Carousel View",
      description: "Swipe through your favorite outfits and collections",
      icon: Grid3X3,
      features: ["Swipe Navigation", "Visual Discovery", "Collection Browse"],
      mockupType: "carousel",
      gradient: "from-pink-500/20 to-rose-500/20"
    }
  ];

  // Auto-play functionality
  useEffect(() => {
    if (!isAutoPlaying) return;

    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % mockups.length);
    }, 5000);

    return () => clearInterval(interval);
  }, [isAutoPlaying, mockups.length]);

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % mockups.length);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + mockups.length) % mockups.length);
  };

  const toggleAutoPlay = () => {
    setIsAutoPlaying(!isAutoPlaying);
  };

  return (
    <section className="py-16 md:py-24 lg:py-32 relative overflow-hidden">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header Section */}
        <div className="text-center mb-12 md:mb-16 lg:mb-20">
          <div className="inline-flex items-center gap-2 px-3 py-2 sm:px-4 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 mb-4 md:mb-6">
            <div className="w-2 h-2 bg-emerald-400 rounded-full animate-pulse"></div>
            <span className="text-white/90 text-xs sm:text-sm font-medium">Live Preview</span>
          </div>
          <h2 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-light text-white mb-4 md:mb-6 tracking-tight px-4">
            Experience ClosetMate
          </h2>
          <p className="text-base sm:text-lg md:text-xl text-white/70 max-w-2xl lg:max-w-3xl mx-auto font-light leading-relaxed px-4">
            Discover how our intuitive interface transforms your daily styling routine with intelligent recommendations and seamless organization
          </p>
        </div>

        {/* Main Carousel Container */}
        <div className="relative max-w-7xl mx-auto">
          {/* Mockup Display */}
          <div className="relative">
            <div className={`relative h-[400px] sm:h-[500px] md:h-[600px] lg:h-[700px] rounded-2xl md:rounded-3xl overflow-hidden bg-gradient-to-br ${mockups[currentSlide].gradient} backdrop-blur-xl border border-white/20 shadow-2xl`}>
              {/* Background Pattern */}
              <div className="absolute inset-0 opacity-5">
                <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(255,255,255,0.1),transparent_50%)]"></div>
              </div>
              {/* Slide Content */}
              {mockups.map((mockup, index) => (
                <div
                  key={index}
                  className={`absolute inset-0 transition-all duration-1000 ease-out ${index === currentSlide
                    ? 'opacity-100 transform translate-x-0 scale-100'
                    : index < currentSlide
                      ? 'opacity-0 transform -translate-x-full scale-95'
                      : 'opacity-0 transform translate-x-full scale-95'
                    }`}
                >
                  {/* Mockup Content */}
                  <div className="h-full p-8 flex flex-col">
                    {mockup.mockupType === 'dashboard' && <DashboardMockup />}
                    {mockup.mockupType === 'closet' && <ClosetMockup />}
                    {mockup.mockupType === 'schedule' && <ScheduleMockup />}
                    {mockup.mockupType === 'carousel' && <CarouselMockup />}
                  </div>
                </div>
              ))}

              {/* Feature Info Overlay */}
              <div className="absolute bottom-4 md:bottom-6 lg:bottom-8 left-4 md:left-6 lg:left-8 right-4 md:right-6 lg:right-8">
                <div className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-xl md:rounded-2xl p-4 md:p-6 shadow-xl">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 md:gap-3 mb-2 md:mb-3">
                        <div className="p-1.5 md:p-2 bg-white/20 rounded-lg md:rounded-xl">
                          <MockupIcon icon={mockups[currentSlide].icon} />
                        </div>
                        <h3 className="text-lg md:text-xl font-semibold text-white">
                          {mockups[currentSlide].title}
                        </h3>
                      </div>
                      <p className="text-white/80 text-xs md:text-sm mb-3 md:mb-4 leading-relaxed">
                        {mockups[currentSlide].description}
                      </p>
                      <div className="flex flex-wrap gap-1.5 md:gap-2">
                        {mockups[currentSlide].features.map((feature, idx) => (
                          <span
                            key={idx}
                            className="px-2 md:px-3 py-1 bg-white/20 backdrop-blur-sm rounded-full text-xs text-white/90 font-medium"
                          >
                            {feature}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Navigation Controls */}
            <div className="absolute top-4 md:top-6 lg:top-8 right-4 md:right-6 lg:right-8 flex items-center gap-2 md:gap-3">
              <button
                onClick={toggleAutoPlay}
                className="p-2 md:p-3 bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg md:rounded-xl hover:bg-white/20 transition-all duration-300"
              >
                {isAutoPlaying ? (
                  <Pause className="w-3 h-3 md:w-4 md:h-4 text-white" />
                ) : (
                  <Play className="w-3 h-3 md:w-4 md:h-4 text-white" />
                )}
              </button>
            </div>

            {/* Side Navigation - Hidden on mobile */}
            <button
              onClick={prevSlide}
              className="hidden md:block absolute left-2 lg:left-4 top-1/2 -translate-y-1/2 p-2 lg:p-3 bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg lg:rounded-xl hover:bg-white/20 transition-all duration-300 hover:scale-110"
            >
              <ChevronLeft className="w-4 h-4 lg:w-5 lg:h-5 text-white" />
            </button>

            <button
              onClick={nextSlide}
              className="hidden md:block absolute right-2 lg:right-4 top-1/2 -translate-y-1/2 p-2 lg:p-3 bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg lg:rounded-xl hover:bg-white/20 transition-all duration-300 hover:scale-110"
            >
              <ChevronRight className="w-4 h-4 lg:w-5 lg:h-5 text-white" />
            </button>
          </div>

          {/* Bottom Navigation */}
          <div className="flex justify-center items-center mt-8 md:mt-12 gap-3 md:gap-6">
            {/* Mobile: Simple dots */}
            <div className="flex md:hidden items-center gap-2">
              {mockups.map((_, index) => (
                <button
                  key={index}
                  className={`w-2 h-2 rounded-full transition-all duration-300 ${index === currentSlide
                    ? 'bg-white scale-125'
                    : 'bg-white/40'
                    }`}
                  onClick={() => setCurrentSlide(index)}
                />
              ))}
            </div>

            {/* Tablet and Desktop: Full navigation */}
            <div className="hidden md:flex items-center gap-3 lg:gap-4 overflow-x-auto">
              {mockups.map((mockup, index) => (
                <button
                  key={index}
                  className={`group flex items-center gap-2 lg:gap-3 px-3 lg:px-4 py-2 lg:py-3 rounded-lg lg:rounded-xl transition-all duration-500 whitespace-nowrap ${index === currentSlide
                    ? 'bg-white/20 backdrop-blur-sm border border-white/30'
                    : 'hover:bg-white/10 border border-transparent'
                    }`}
                  onClick={() => setCurrentSlide(index)}
                >
                  <div className={`w-2 h-2 rounded-full transition-all duration-300 ${index === currentSlide
                    ? 'bg-white scale-125'
                    : 'bg-white/40 group-hover:bg-white/60'
                    }`}></div>
                  <span className={`text-sm lg:text-base font-medium transition-all duration-300 ${index === currentSlide
                    ? 'text-white'
                    : 'text-white/60 group-hover:text-white/80'
                    }`}>
                    {mockup.title}
                  </span>
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

// Mockup Components
const DashboardMockup = () => (
  <div className="h-full flex flex-col p-2 md:p-4 lg:p-6">
    {/* Header */}
    <div className="flex items-center justify-between mb-4 md:mb-6 lg:mb-8">
      <div className="bg-white/10 backdrop-blur-sm border border-white/20 p-3 md:p-4 rounded-xl md:rounded-2xl flex-1 mr-3">
        <div className="flex items-center gap-2 md:gap-3">
          <Sun className="w-4 h-4 md:w-5 md:h-5 text-yellow-400 flex-shrink-0" />
          <div className="min-w-0">
            <span className="text-white text-base md:text-lg font-semibold block">22°C</span>
            <p className="text-white/70 text-xs md:text-sm truncate">Perfect for light layers</p>
          </div>
        </div>
      </div>
      <div className="bg-white/10 backdrop-blur-sm border border-white/20 p-2 md:p-3 rounded-full flex-shrink-0">
        <div className="w-8 h-8 md:w-10 md:h-10 bg-gradient-to-br from-emerald-400 to-blue-500 rounded-full flex items-center justify-center">
          <div className="w-4 h-4 md:w-6 md:h-6 bg-white/90 rounded-full"></div>
        </div>
      </div>
    </div>

    {/* Today's Recommendation */}
    <div className="bg-white/10 backdrop-blur-sm border border-white/20 p-3 md:p-4 lg:p-6 rounded-2xl md:rounded-3xl mb-4 md:mb-6 flex-1">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4 md:mb-6 gap-2">
        <h4 className="text-white text-base md:text-lg lg:text-xl font-semibold">Today's Perfect Match</h4>
        <div className="px-2 md:px-3 py-1 bg-emerald-500/20 border border-emerald-400/30 rounded-full self-start sm:self-auto">
          <span className="text-emerald-300 text-xs md:text-sm font-medium">AI Recommended</span>
        </div>
      </div>

      <div className="grid grid-cols-3 gap-2 md:gap-3 lg:gap-4">
        <div className="group cursor-pointer">
          <div className="bg-white/10 backdrop-blur-sm border border-white/20 p-2 md:p-3 lg:p-4 rounded-xl md:rounded-2xl text-center hover:bg-white/20 transition-all duration-300 group-hover:scale-105">
            <div className="w-full h-16 md:h-20 lg:h-24 rounded-lg md:rounded-xl mb-2 md:mb-3 overflow-hidden bg-gradient-to-br from-white/10 to-white/5">
              <img
                src="/images/stock/white-shirt.jpg"
                alt="White Shirt"
                className="w-full h-full object-cover"
              />
            </div>
            <span className="text-white/90 text-xs md:text-sm font-medium block">Classic White</span>
            <p className="text-white/60 text-xs mt-1 hidden sm:block">Cotton Shirt</p>
          </div>
        </div>

        <div className="group cursor-pointer">
          <div className="bg-white/10 backdrop-blur-sm border border-white/20 p-2 md:p-3 lg:p-4 rounded-xl md:rounded-2xl text-center hover:bg-white/20 transition-all duration-300 group-hover:scale-105">
            <div className="w-full h-16 md:h-20 lg:h-24 rounded-lg md:rounded-xl mb-2 md:mb-3 overflow-hidden bg-gradient-to-br from-white/10 to-white/5">
              <img
                src="/images/stock/blue-jeans.jpg"
                alt="Blue Jeans"
                className="w-full h-full object-cover"
              />
            </div>
            <span className="text-white/90 text-xs md:text-sm font-medium block">Denim Blue</span>
            <p className="text-white/60 text-xs mt-1 hidden sm:block">Slim Fit</p>
          </div>
        </div>

        <div className="group cursor-pointer">
          <div className="bg-white/10 backdrop-blur-sm border border-white/20 p-2 md:p-3 lg:p-4 rounded-xl md:rounded-2xl text-center hover:bg-white/20 transition-all duration-300 group-hover:scale-105">
            <div className="w-full h-16 md:h-20 lg:h-24 rounded-lg md:rounded-xl mb-2 md:mb-3 overflow-hidden bg-gradient-to-br from-white/10 to-white/5">
              <img
                src="/images/stock/white-sneakers.jpg"
                alt="White Sneakers"
                className="w-full h-full object-cover"
              />
            </div>
            <span className="text-white/90 text-xs md:text-sm font-medium block">Clean White</span>
            <p className="text-white/60 text-xs mt-1 hidden sm:block">Sneakers</p>
          </div>
        </div>
      </div>
    </div>

    {/* Quick Stats */}
    <div className="grid grid-cols-2 gap-2 md:gap-3 lg:gap-4">
      <div className="bg-white/10 backdrop-blur-sm border border-white/20 p-3 md:p-4 rounded-xl md:rounded-2xl text-center">
        <div className="text-white text-lg md:text-xl lg:text-2xl font-bold mb-1">127</div>
        <div className="text-white/70 text-xs md:text-sm">Total Items</div>
      </div>
      <div className="bg-white/10 backdrop-blur-sm border border-white/20 p-3 md:p-4 rounded-xl md:rounded-2xl text-center">
        <div className="text-white text-lg md:text-xl lg:text-2xl font-bold mb-1">8</div>
        <div className="text-white/70 text-xs md:text-sm">Saved Outfits</div>
      </div>
    </div>
  </div>
);

const ClosetMockup = () => {
  const clothingItems = [
    { name: 'Classic White', category: 'Shirts', image: '/images/stock/white-shirt.jpg', color: 'White' },
    { name: 'Denim Blue', category: 'Jeans', image: '/images/stock/blue-jeans.jpg', color: 'Blue' },
    { name: 'Elegant Black', category: 'Jackets', image: '/images/stock/black-jacket.jpg', color: 'Black' },
    { name: 'Summer Floral', category: 'Dresses', image: '/images/stock/summer-dress.jpg', color: 'Floral' },
    { name: 'Clean White', category: 'Shoes', image: '/images/stock/white-sneakers.jpg', color: 'White' },
    { name: 'Casual Stripe', category: 'Shirts', image: '/images/stock/striped-shirt.jpg', color: 'Striped' },
    { name: 'Evening Elegant', category: 'Dresses', image: '/images/stock/elegant-dress.jpg', color: 'Black' },
    { name: 'Denim Classic', category: 'Jackets', image: '/images/stock/denim-jacket.jpg', color: 'Blue' },
    { name: 'Casual Brown', category: 'Shoes', image: '/images/stock/brown-boots.jpg', color: 'Brown' }
  ];

  return (
    <div className="h-full flex flex-col p-2 md:p-4 lg:p-6">
      {/* Search Bar */}
      <div className="bg-white/10 backdrop-blur-sm border border-white/20 p-3 md:p-4 rounded-xl md:rounded-2xl mb-4 md:mb-6">
        <div className="flex items-center gap-2 md:gap-3">
          <div className="w-4 h-4 md:w-5 md:h-5 bg-white/30 rounded-full flex items-center justify-center flex-shrink-0">
            <div className="w-2 h-2 md:w-3 md:h-3 bg-white/70 rounded-full"></div>
          </div>
          <span className="text-white/80 text-xs md:text-sm">Search your closet...</span>
        </div>
      </div>

      {/* Categories */}
      <div className="flex gap-2 md:gap-3 mb-4 md:mb-6 overflow-x-auto pb-1">
        {['All', 'Shirts', 'Jeans', 'Dresses', 'Shoes'].map((category, idx) => (
          <div
            key={category}
            className={`px-3 md:px-4 py-1.5 md:py-2 rounded-lg md:rounded-xl text-xs md:text-sm font-medium whitespace-nowrap transition-all duration-300 ${
              idx === 0
                ? 'bg-white/20 backdrop-blur-sm border border-white/30 text-white'
                : 'bg-white/10 backdrop-blur-sm border border-white/20 text-white/70 hover:text-white hover:bg-white/15'
            }`}
          >
            {category}
          </div>
        ))}
      </div>

      {/* Closet Grid */}
      <div className="grid grid-cols-3 gap-2 md:gap-3 lg:gap-4 flex-1 overflow-y-auto">
        {clothingItems.slice(0, 6).map((item, idx) => (
          <div key={idx} className="group cursor-pointer">
            <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl md:rounded-2xl p-2 md:p-3 hover:bg-white/20 transition-all duration-300 group-hover:scale-105">
              <div className="w-full h-20 md:h-24 lg:h-28 rounded-lg md:rounded-xl mb-2 md:mb-3 overflow-hidden bg-gradient-to-br from-white/10 to-white/5">
                <img
                  src={item.image}
                  alt={item.name}
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="text-white/90 text-xs font-medium text-center mb-1 truncate">
                {item.name}
              </div>
              <div className="text-white/60 text-xs text-center hidden sm:block">
                {item.category}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

const ScheduleMockup = () => {
  const weekSchedule = [
    {
      day: 'Mon',
      date: '15',
      event: 'Work Meeting',
      time: '9:00 AM',
      outfit: 'Business Casual',
      items: ['/images/stock/white-shirt.jpg', '/images/stock/black-pants.jpg', '/images/stock/black-jacket.jpg'],
      weather: '22°C'
    },
    {
      day: 'Tue',
      date: '16',
      event: 'Lunch Date',
      time: '12:30 PM',
      outfit: 'Smart Casual',
      items: ['/images/stock/elegant-dress.jpg', '/images/stock/denim-jacket.jpg', '/images/stock/white-sneakers.jpg'],
      weather: '25°C'
    },
    {
      day: 'Wed',
      date: '17',
      event: 'Coffee Meeting',
      time: '3:00 PM',
      outfit: 'Casual Chic',
      items: ['/images/stock/striped-shirt.jpg', '/images/stock/blue-jeans.jpg', '/images/stock/brown-boots.jpg'],
      weather: '20°C'
    },
    {
      day: 'Thu',
      date: '18',
      event: 'Dinner Out',
      time: '7:00 PM',
      outfit: 'Evening Wear',
      items: ['/images/stock/elegant-dress.jpg', '/images/stock/black-jacket.jpg', '/images/stock/brown-boots.jpg'],
      weather: '18°C'
    }
  ];

  return (
    <div className="h-full flex flex-col p-2 md:p-4 lg:p-6">
      {/* Calendar Header */}
      <div className="flex items-center justify-between mb-4 md:mb-6">
        <div className="min-w-0 flex-1">
          <h4 className="text-white text-base md:text-lg lg:text-xl font-semibold">This Week</h4>
          <p className="text-white/70 text-xs md:text-sm">Your planned outfits</p>
        </div>
        <div className="bg-white/10 backdrop-blur-sm border border-white/20 p-2 md:p-3 rounded-lg md:rounded-xl flex-shrink-0">
          <Calendar className="w-4 h-4 md:w-5 md:h-5 text-white/80" />
        </div>
      </div>

      {/* Week View */}
      <div className="space-y-3 md:space-y-4 flex-1 overflow-y-auto">
        {weekSchedule.slice(0, 3).map((item, idx) => (
          <div key={idx} className="bg-white/10 backdrop-blur-sm border border-white/20 p-3 md:p-4 lg:p-5 rounded-xl md:rounded-2xl hover:bg-white/15 transition-all duration-300 group cursor-pointer">
            <div className="flex items-center justify-between gap-3">
              <div className="flex items-center gap-3 md:gap-4 min-w-0 flex-1">
                <div className="bg-white/20 backdrop-blur-sm border border-white/30 p-2 md:p-3 rounded-lg md:rounded-xl text-center min-w-[2.5rem] md:min-w-[3.5rem] flex-shrink-0">
                  <div className="text-white text-sm md:text-lg font-bold">{item.date}</div>
                  <div className="text-white/70 text-xs font-medium">{item.day}</div>
                </div>
                <div className="min-w-0 flex-1">
                  <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-2 mb-1">
                    <h5 className="text-white text-sm md:text-base font-semibold truncate">{item.event}</h5>
                    <span className="px-2 py-1 bg-blue-500/20 border border-blue-400/30 rounded-full text-blue-300 text-xs font-medium self-start sm:self-auto">
                      {item.time}
                    </span>
                  </div>
                  <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-3">
                    <span className="text-white/80 text-xs md:text-sm">{item.outfit}</span>
                    <span className="text-white/60 text-xs md:text-sm hidden sm:inline">• {item.weather}</span>
                  </div>
                </div>
              </div>
              <div className="flex gap-1 md:gap-2 flex-shrink-0">
                {item.items.slice(0, 3).map((itemImg, i) => (
                  <div
                    key={i}
                    className="w-8 h-8 md:w-10 md:h-10 lg:w-12 lg:h-12 rounded-lg md:rounded-xl bg-white/10 backdrop-blur-sm border border-white/20 overflow-hidden group-hover:scale-110 transition-all duration-300"
                  >
                    <img
                      src={itemImg}
                      alt={`Item ${i + 1}`}
                      className="w-full h-full object-cover"
                    />
                  </div>
                ))}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

const CarouselMockup = () => {
  const collections = [
    {
      name: 'Summer Essentials',
      description: 'Perfect for warm weather adventures',
      count: '8 items',
      items: [
        { name: 'Linen Shirt', image: '/images/stock/white-shirt.jpg', price: '$45' },
        { name: 'Floral Dress', image: '/images/stock/summer-dress.jpg', price: '$65' },
        { name: 'Clean Sneakers', image: '/images/stock/white-sneakers.jpg', price: '$85' },
        { name: 'Denim Jacket', image: '/images/stock/denim-jacket.jpg', price: '$75' }
      ]
    }
  ];

  return (
    <div className="h-full flex flex-col p-2 md:p-4 lg:p-6">
      {/* Carousel Header */}
      <div className="flex items-center justify-between mb-4 md:mb-6">
        <div className="min-w-0 flex-1">
          <h4 className="text-white text-base md:text-lg lg:text-xl font-semibold">Your Collections</h4>
          <p className="text-white/70 text-xs md:text-sm">Curated outfit combinations</p>
        </div>
        <div className="flex gap-1.5 md:gap-2 flex-shrink-0">
          <div className="w-2 h-2 md:w-3 md:h-3 bg-white rounded-full"></div>
          <div className="w-2 h-2 md:w-3 md:h-3 bg-white/40 rounded-full"></div>
          <div className="w-2 h-2 md:w-3 md:h-3 bg-white/40 rounded-full"></div>
        </div>
      </div>

      {/* Main Carousel Item */}
      <div className="bg-white/10 backdrop-blur-sm border border-white/20 p-3 md:p-4 lg:p-6 rounded-2xl md:rounded-3xl mb-4 md:mb-6 flex-1">
        <div className="text-center mb-4 md:mb-6">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-center gap-2 sm:gap-3 mb-2">
            <h5 className="text-white text-base md:text-lg font-semibold">{collections[0].name}</h5>
            <span className="px-2 md:px-3 py-1 bg-emerald-500/20 border border-emerald-400/30 rounded-full text-emerald-300 text-xs font-medium self-center sm:self-auto">
              {collections[0].count}
            </span>
          </div>
          <p className="text-white/70 text-xs md:text-sm">{collections[0].description}</p>
        </div>

        <div className="grid grid-cols-2 gap-2 md:gap-3 lg:gap-4 mb-4 md:mb-6">
          {collections[0].items.slice(0, 4).map((item, idx) => (
            <div key={idx} className="group cursor-pointer">
              <div className="bg-white/10 backdrop-blur-sm border border-white/20 p-2 md:p-3 lg:p-4 rounded-xl md:rounded-2xl text-center hover:bg-white/20 transition-all duration-300 group-hover:scale-105">
                <div className="w-full h-20 md:h-24 lg:h-32 rounded-lg md:rounded-xl mb-2 md:mb-3 overflow-hidden bg-gradient-to-br from-white/10 to-white/5">
                  <img
                    src={item.image}
                    alt={item.name}
                    className="w-full h-full object-cover"
                  />
                </div>
                <div className="flex items-center justify-between">
                  <div className="text-left min-w-0 flex-1">
                    <span className="text-white/90 text-xs md:text-sm font-medium block truncate">{item.name}</span>
                    <span className="text-white/60 text-xs hidden sm:block">{item.price}</span>
                  </div>
                  <div className="w-4 h-4 md:w-6 md:h-6 bg-white/20 rounded-full flex items-center justify-center flex-shrink-0 ml-2">
                    <div className="w-1.5 h-1.5 md:w-2 md:h-2 bg-white rounded-full"></div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="flex justify-center">
          <button className="bg-white/20 backdrop-blur-sm border border-white/30 px-4 md:px-6 py-2 md:py-3 rounded-lg md:rounded-xl hover:bg-white/30 transition-all duration-300 group">
            <span className="text-white text-xs md:text-sm font-medium group-hover:scale-105 transition-transform duration-300">View Full Collection</span>
          </button>
        </div>
      </div>

      {/* Thumbnail Navigation */}
      <div className="flex gap-2 md:gap-3 justify-center overflow-x-auto pb-1">
        {['Summer', 'Work', 'Evening', 'Casual'].map((collection, idx) => (
          <button
            key={collection}
            className={`px-3 md:px-4 py-1.5 md:py-2 rounded-lg md:rounded-xl text-xs md:text-sm font-medium transition-all duration-300 whitespace-nowrap ${
              idx === 0
                ? 'bg-white/20 backdrop-blur-sm border border-white/30 text-white'
                : 'bg-white/10 backdrop-blur-sm border border-white/20 text-white/70 hover:text-white hover:bg-white/15'
            }`}
          >
            {collection}
          </button>
        ))}
      </div>
    </div>
  );
};

// Helper component for rendering icons
const MockupIcon = ({ icon: Icon }: { icon: React.ComponentType<{ className?: string }> }) => (
  <Icon className="w-4 h-4 md:w-5 md:h-5 text-white" />
);

export default MockupShowcase;
